<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iOS 18 风格天气卡片</title>
    <style>
        :root {
            --card-width: 280px;
            --card-height: 380px;
            --card-radius: 24px;
            --card-spacing: 24px;
            --primary-text: rgba(255, 255, 255, 0.95);
            --secondary-text: rgba(255, 255, 255, 0.7);
            --card-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
            --card-glow: 0 0 20px rgba(255, 255, 255, 0.05);
            --transition-timing: cubic-bezier(0.42, 0, 0.58, 1);
            --bg-gradient-dark: linear-gradient(135deg, #121212, #1e1e1e, #121212);
            --bg-gradient-light: linear-gradient(135deg, #e0e0e0, #f5f5f5, #e0e0e0);
            --theme-toggle-bg-dark: rgba(255, 255, 255, 0.1);
            --theme-toggle-bg-light: rgba(0, 0, 0, 0.1);
        }
        
        :root.light-theme {
            --primary-text: rgba(0, 0, 0, 0.9);
            --secondary-text: rgba(0, 0, 0, 0.6);
            --card-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
            --card-glow: 0 0 20px rgba(0, 0, 0, 0.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        body {
            min-height: 100vh;
            background: var(--bg-gradient-dark);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            overflow-x: hidden;
            transition: background 0.5s ease;
        }
        
        :root.light-theme body {
            background: var(--bg-gradient-light);
        }
        
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--theme-toggle-bg-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 100;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: background 0.3s ease;
        }
        
        :root.light-theme .theme-toggle {
            background: var(--theme-toggle-bg-light);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .theme-toggle svg {
            width: 24px;
            height: 24px;
            color: var(--primary-text);
            transition: transform 0.5s ease, opacity 0.3s ease;
        }
        
        .theme-toggle .sun-icon {
            opacity: 1;
            position: absolute;
        }
        
        .theme-toggle .moon-icon {
            opacity: 0;
            position: absolute;
            transform: rotate(90deg) scale(0);
        }
        
        :root.light-theme .theme-toggle .sun-icon {
            opacity: 0;
            transform: rotate(-90deg) scale(0);
        }
        
        :root.light-theme .theme-toggle .moon-icon {
            opacity: 1;
            transform: rotate(0) scale(1);
        }

        h1 {
            color: var(--primary-text);
            font-weight: 600;
            font-size: 2.5rem;
            margin-bottom: 40px;
            text-align: center;
            letter-spacing: -0.5px;
        }

        .container {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: var(--card-spacing);
            max-width: 1280px;
            margin: 0 auto;
        }

        .weather-card {
            width: var(--card-width);
            height: var(--card-height);
            border-radius: var(--card-radius);
            overflow: hidden;
            position: relative;
            box-shadow: var(--card-shadow);
            transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
            backdrop-filter: blur(10px);
            transform-style: preserve-3d;
            border: 1px solid rgba(255, 255, 255, 0.1);
            animation: card-appear 0.8s var(--transition-timing) forwards;
            opacity: 0;
            transform: translateY(30px) scale(0.9);
        }
        
        .weather-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(125deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.05) 30%, rgba(255, 255, 255, 0) 50%);
            z-index: 2;
            pointer-events: none;
        }
        
        .weather-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--card-shadow), var(--card-glow);
        }
        
        @keyframes card-appear {
            0% {
                opacity: 0;
                transform: translateY(30px) scale(0.9);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        /* 初始加载时的延迟动画 */
        .container .weather-card:nth-child(1) {
            animation-delay: 0.1s;
        }
        
        .container .weather-card:nth-child(2) {
            animation-delay: 0.3s;
        }
        
        .container .weather-card:nth-child(3) {
            animation-delay: 0.5s;
        }
        
        .container .weather-card:nth-child(4) {
            animation-delay: 0.7s;
        }

        .card-content {
            position: absolute;
            bottom: 0;
            width: 100%;
            padding: 30px;
            z-index: 10;
            color: var(--primary-text);
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0));
            height: 60%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            backdrop-filter: blur(5px);
            transform: translateY(10px);
            opacity: 0;
            animation: slide-up 0.8s var(--transition-timing) forwards;
            animation-delay: 0.3s;
        }
        
        @keyframes slide-up {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .weather-icon {
            position: absolute;
            top: -60px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(8px);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.15) inset;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--primary-text);
            transform: translateZ(20px);
            transition: all 0.3s ease;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0% { transform: translateY(0) translateZ(20px); }
            50% { transform: translateY(-10px) translateZ(20px); }
            100% { transform: translateY(0) translateZ(20px); }
        }

        .temperature {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 5px;
            line-height: 1;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .weather-type {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .details {
            color: var(--secondary-text);
            font-size: 0.95rem;
            display: flex;
            justify-content: space-between;
        }

        .animation-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        /* 晴天卡片 */
        .sunny-card {
            background: linear-gradient(to bottom, #4d95e0, #64b5f6);
        }

        .sun {
            position: absolute;
            width: 120px;
            height: 120px;
            background: radial-gradient(circle, #ffeb3b, #ff9800);
            border-radius: 50%;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 0 60px #ff9800, 0 0 100px #ffeb3b;
            animation: pulse 4s infinite alternate;
        }

        .sun::after {
            content: '';
            position: absolute;
            top: -30px;
            left: -30px;
            right: -30px;
            bottom: -30px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 235, 59, 0.3), transparent 70%);
            animation: glow 4s infinite alternate;
        }
        
        .sun-rays {
            position: absolute;
            top: 60px;
            left: 50%;
            width: 180px;
            height: 180px;
            transform: translateX(-50%);
            z-index: 1;
        }
        
        .ray {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 3px;
            background: rgba(255, 255, 255, 0.3);
            transform-origin: 0 0;
        }

        @keyframes pulse {
            0% {
                transform: translateX(-50%) scale(1);
            }
            100% {
                transform: translateX(-50%) scale(1.1);
            }
        }

        @keyframes glow {
            0% {
                opacity: 0.5;
            }
            100% {
                opacity: 1;
            }
        }

        /* 雨天卡片 */
        .rainy-card {
            background: linear-gradient(to bottom, #546e7a, #37474f);
            position: relative;
        }

        .cloud {
            position: absolute;
            width: 180px;
            height: 60px;
            background: #e0e0e0;
            border-radius: 50px;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2), inset 0 -5px 15px rgba(0, 0, 0, 0.1);
            z-index: 2;
        }

        .cloud::before {
            content: '';
            position: absolute;
            width: 80px;
            height: 80px;
            background: #e0e0e0;
            border-radius: 50%;
            top: -40px;
            left: 30px;
        }

        .cloud::after {
            content: '';
            position: absolute;
            width: 100px;
            height: 100px;
            background: #e0e0e0;
            border-radius: 50%;
            top: -50px;
            right: 30px;
        }

        .rain {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .drop {
            position: absolute;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.5));
            width: 2px;
            height: 20px;
            border-radius: 0 0 5px 5px;
            animation: rain-fall linear infinite;
            filter: drop-shadow(0 0 1px rgba(255, 255, 255, 0.5));
        }
        
        .drop::before {
            content: '';
            position: absolute;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            bottom: -3px;
            left: -2px;
            opacity: 0;
            transform: scale(0);
            animation: drop-splash 1s linear infinite;
            animation-delay: inherit;
        }
        
        @keyframes rain-fall {
            0% {
                transform: translateY(-120px) rotate(0deg);
                opacity: 0;
            }
            5% {
                transform: translateY(-100px) rotate(0deg);
                opacity: 0;
            }
            10% {
                transform: translateY(-80px) rotate(0deg);
                opacity: 1;
            }
            90% {
                opacity: 1;
                height: 20px;
            }
            95% {
                height: 5px;
            }
            100% {
                transform: translateY(350px) rotate(20deg);
                opacity: 0;
                height: 0px;
            }
        }
        
        @keyframes drop-splash {
            0%, 85% {
                opacity: 0;
                transform: scale(0);
            }
            90% {
                opacity: 1;
                transform: scale(1);
            }
            100% {
                opacity: 0;
                transform: scale(2);
            }
        }

        /* 风天卡片 */
        .windy-card {
            background: linear-gradient(to bottom, #78909c, #546e7a);
            position: relative;
        }
        
        .windy-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0) 100%);
            background-size: 200% 100%;
            animation: wind-gradient 8s ease-in-out infinite;
        }
        
        @keyframes wind-gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .wind-container {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        
        .wind-icon {
            position: absolute;
            top: 40px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .wind {
            position: absolute;
            height: 3px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.6);
            animation: wind-blow linear infinite;
        }

        @keyframes wind-blow {
            0% {
                transform: translateX(-100px);
                opacity: 0;
            }
            10% {
                opacity: 0.8;
            }
            90% {
                opacity: 0.8;
            }
            100% {
                transform: translateX(380px);
                opacity: 0;
            }
        }

        .leaf {
            position: absolute;
            width: 15px;
            height: 15px;
            background: rgba(139, 195, 74, 0.8);
            clip-path: ellipse(50% 100% at 50% 50%);
            animation: leaf-blow linear infinite;
        }

        @keyframes leaf-blow {
            0% {
                transform: translateX(-50px) translateY(0) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            100% {
                transform: translateX(330px) translateY(20px) rotate(360deg);
                opacity: 0;
            }
        }

        /* 雪天卡片 */
        .snowy-card {
            background: linear-gradient(to bottom, #90a4ae, #78909c);
            position: relative;
        }
        
        .snowy-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
            animation: snow-glow 4s ease-in-out infinite alternate;
        }
        
        @keyframes snow-glow {
            0% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .snow-cloud {
            position: absolute;
            width: 180px;
            height: 60px;
            background: #e0e0e0;
            border-radius: 50px;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2), inset 0 -5px 15px rgba(0, 0, 0, 0.1);
            z-index: 2;
        }
        
        .snow-sparkles {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 3;
            pointer-events: none;
        }
        
        .sparkle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: white;
            border-radius: 50%;
            box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
            opacity: 0;
            animation: sparkle 5s linear infinite;
        }
        
        @keyframes sparkle {
            0% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: scale(0); }
        }

        .snow-cloud::before {
            content: '';
            position: absolute;
            width: 80px;
            height: 80px;
            background: #e0e0e0;
            border-radius: 50%;
            top: -40px;
            left: 30px;
        }

        .snow-cloud::after {
            content: '';
            position: absolute;
            width: 100px;
            height: 100px;
            background: #e0e0e0;
            border-radius: 50%;
            top: -50px;
            right: 30px;
        }

        .snowflake {
            position: absolute;
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            animation: snow-fall linear infinite;
            box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
            filter: blur(0.5px);
        }
        
        .snowflake::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.8);
            transform: scale(0.2);
            filter: blur(1px);
            animation: snow-pulse 3s ease-in-out infinite alternate;
        }

        @keyframes snow-fall {
            0% {
                transform: translateY(-120px) translateX(0) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.3;
            }
            20% {
                transform: translateY(-80px) translateX(10px) rotate(45deg);
                opacity: 0.6;
            }
            40% {
                transform: translateY(0px) translateX(-5px) rotate(90deg);
                opacity: 0.8;
            }
            60% {
                transform: translateY(120px) translateX(15px) rotate(180deg);
                opacity: 1;
            }
            80% {
                transform: translateY(240px) translateX(-10px) rotate(270deg);
                opacity: 0.8;
            }
            100% {
                transform: translateY(350px) translateX(20px) rotate(360deg);
                opacity: 0;
            }
        }
        
        @keyframes snow-pulse {
            0% {
                transform: scale(0.2);
                opacity: 0.5;
            }
            100% {
                transform: scale(1.2);
                opacity: 0.1;
            }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            :root {
                --card-width: 260px;
                --card-height: 350px;
                --card-spacing: 20px;
            }
        }

        @media (max-width: 768px) {
            :root {
                --card-width: 300px;
                --card-height: 380px;
            }
            
            h1 {
                font-size: 2rem;
                margin-bottom: 30px;
            }
            
            .theme-toggle {
                top: 15px;
                right: 15px;
                width: 45px;
                height: 45px;
            }
            
            .container {
                flex-direction: column;
                align-items: center;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 20px 10px;
            }
            
            h1 {
                font-size: 1.8rem;
                margin-bottom: 20px;
            }
            
            .theme-toggle {
                top: 10px;
                right: 10px;
                width: 40px;
                height: 40px;
            }
            
            .theme-toggle svg {
                width: 20px;
                height: 20px;
            }
            
            .weather-card {
                height: 340px;
            }
            
            .temperature {
                font-size: 3rem;
            }
            
            .weather-type {
                font-size: 1.5rem;
            }
            
            .details {
                font-size: 0.85rem;
            }
        }

        /* 加载动画 */
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #121212;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100;
            transition: opacity 0.5s ease-out;
        }

        .loading.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-left-color: #64b5f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body>
    <div class="loading">
        <div class="spinner"></div>
    </div>
    
    <div class="theme-toggle" id="themeToggle">
        <svg class="sun-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="5"></circle>
            <line x1="12" y1="1" x2="12" y2="3"></line>
            <line x1="12" y1="21" x2="12" y2="23"></line>
            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
            <line x1="1" y1="12" x2="3" y2="12"></line>
            <line x1="21" y1="12" x2="23" y2="12"></line>
            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
        </svg>
        <svg class="moon-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
        </svg>
    </div>

    <h1>iOS 18 天气卡片</h1>
    
    <div class="container">
        <!-- 晴天卡片 -->
        <div class="weather-card sunny-card">
            <div class="animation-container">
                <div class="sun"></div>
                <div class="sun-rays"></div>
            </div>
            <div class="card-content">
                <div class="weather-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                </div>
                <div class="temperature">28°</div>
                <div class="weather-type">晴天</div>
                <div class="details">
                    <span>湿度: 35%</span>
                    <span>风速: 3 km/h</span>
                </div>
            </div>
        </div>

        <!-- 雨天卡片 -->
        <div class="weather-card rainy-card">
            <div class="animation-container">
                <div class="cloud"></div>
                <div class="rain"></div>
            </div>
            <div class="card-content">
                <div class="weather-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 16.2A4.5 4.5 0 0 0 17.5 8h-1.8A7 7 0 1 0 4 14.9"></path>
                        <path d="M16 14v6"></path>
                        <path d="M8 14v6"></path>
                        <path d="M12 16v6"></path>
                    </svg>
                </div>
                <div class="temperature">18°</div>
                <div class="weather-type">降雨</div>
                <div class="details">
                    <span>湿度: 85%</span>
                    <span>风速: 8 km/h</span>
                </div>
            </div>
        </div>

        <!-- 风天卡片 -->
        <div class="weather-card windy-card">
            <div class="animation-container">
                <div class="wind-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="rgba(255,255,255,0.7)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M9.59 4.59A2 2 0 1 1 11 8H2m10.59 11.41A2 2 0 1 0 14 16H2m15.73-8.27A2.5 2.5 0 1 1 19.5 12H2"></path>
                    </svg>
                </div>
                <div class="wind-container"></div>
            </div>
            <div class="card-content">
                <div class="weather-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M17.7 7.7a2.5 2.5 0 1 1 1.8 4.3H2"></path>
                        <path d="M9.6 4.6A2 2 0 1 1 11 8H2"></path>
                        <path d="M12.6 19.4A2 2 0 1 0 14 16H2"></path>
                    </svg>
                </div>
                <div class="temperature">22°</div>
                <div class="weather-type">大风</div>
                <div class="details">
                    <span>湿度: 45%</span>
                    <span>风速: 25 km/h</span>
                </div>
            </div>
        </div>

        <!-- 雪天卡片 -->
        <div class="weather-card snowy-card">
            <div class="animation-container">
                <div class="snow-cloud"></div>
                <div class="snow"></div>
                <div class="snow-sparkles"></div>
            </div>
            <div class="card-content">
                <div class="weather-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M20 17.58A5 5 0 0 0 18 8h-1.26A8 8 0 1 0 4 16.25"></path>
                        <path d="M8 16h.01"></path>
                        <path d="M8 20h.01"></path>
                        <path d="M12 18h.01"></path>
                        <path d="M12 22h.01"></path>
                        <path d="M16 16h.01"></path>
                        <path d="M16 20h.01"></path>
                    </svg>
                </div>
                <div class="temperature">-2°</div>
                <div class="weather-type">下雪</div>
                <div class="details">
                    <span>湿度: 70%</span>
                    <span>风速: 6 km/h</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载动画
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.querySelector('.loading').classList.add('hidden');
            }, 1000);
        });

        // 创建太阳光线
        function createSunRays() {
            const sunRays = document.querySelector('.sun-rays');
            const numberOfRays = 12;
            
            for (let i = 0; i < numberOfRays; i++) {
                const ray = document.createElement('div');
                ray.classList.add('ray');
                
                const angle = (i / numberOfRays) * 360;
                const delay = i * 0.1;
                
                ray.style.transform = `rotate(${angle}deg)`;
                ray.style.animationDelay = `${delay}s`;
                ray.style.opacity = 0.5 + Math.random() * 0.5;
                
                sunRays.appendChild(ray);
            }
        }
        
        // 创建雨滴
        function createRaindrops() {
            const rain = document.querySelector('.rain');
            const numberOfDrops = 40;
            
            for (let i = 0; i < numberOfDrops; i++) {
                const drop = document.createElement('div');
                drop.classList.add('drop');
                
                // 随机位置和延迟
                const left = Math.random() * 100;
                const delay = Math.random() * 2;
                const duration = 1 + Math.random() * 0.5;
                
                drop.style.left = `${left}%`;
                drop.style.animationDelay = `${delay}s`;
                drop.style.animationDuration = `${duration}s`;
                
                rain.appendChild(drop);
            }
        }

        // 创建风元素
        function createWindElements() {
            const windContainer = document.querySelector('.wind-container');
            const numberOfWinds = 15;
            const numberOfLeaves = 8;
            
            // 创建风线
            for (let i = 0; i < numberOfWinds; i++) {
                const wind = document.createElement('div');
                wind.classList.add('wind');
                
                const top = 50 + Math.random() * 200;
                const width = 50 + Math.random() * 100;
                const delay = Math.random() * 3;
                const duration = 2 + Math.random() * 1;
                const opacity = 0.3 + Math.random() * 0.3;
                
                wind.style.top = `${top}px`;
                wind.style.width = `${width}px`;
                wind.style.animationDelay = `${delay}s`;
                wind.style.animationDuration = `${duration}s`;
                wind.style.opacity = opacity;
                
                windContainer.appendChild(wind);
            }
            
            // 创建树叶
            for (let i = 0; i < numberOfLeaves; i++) {
                const leaf = document.createElement('div');
                leaf.classList.add('leaf');
                
                const top = 100 + Math.random() * 150;
                const delay = Math.random() * 5;
                const duration = 3 + Math.random() * 2;
                const rotation = Math.random() * 360;
                
                leaf.style.top = `${top}px`;
                leaf.style.animationDelay = `${delay}s`;
                leaf.style.animationDuration = `${duration}s`;
                leaf.style.transform = `rotate(${rotation}deg)`;
                
                windContainer.appendChild(leaf);
            }
        }

        // 创建雪花
        function createSnowflakes() {
            const snow = document.createElement('div');
            snow.classList.add('snow');
            document.querySelector('.snowy-card .animation-container').appendChild(snow);
            
            const numberOfFlakes = 50;
            
            for (let i = 0; i < numberOfFlakes; i++) {
                const flake = document.createElement('div');
                flake.classList.add('snowflake');
                
                const size = 3 + Math.random() * 5;
                const left = Math.random() * 100;
                const delay = Math.random() * 5;
                const duration = 5 + Math.random() * 5;
                const opacity = 0.6 + Math.random() * 0.4;
                
                flake.style.width = `${size}px`;
                flake.style.height = `${size}px`;
                flake.style.left = `${left}%`;
                flake.style.animationDelay = `${delay}s`;
                flake.style.animationDuration = `${duration}s`;
                flake.style.opacity = opacity;
                
                snow.appendChild(flake);
            }
            
            // 添加闪烁效果
            createSnowSparkles();
        }
        
        // 创建雪花闪烁效果
        function createSnowSparkles() {
            const sparklesContainer = document.querySelector('.snow-sparkles');
            const numberOfSparkles = 20;
            
            for (let i = 0; i < numberOfSparkles; i++) {
                const sparkle = document.createElement('div');
                sparkle.classList.add('sparkle');
                
                const top = Math.random() * 100;
                const left = Math.random() * 100;
                const delay = Math.random() * 10;
                
                sparkle.style.top = `${top}%`;
                sparkle.style.left = `${left}%`;
                sparkle.style.animationDelay = `${delay}s`;
                
                sparklesContainer.appendChild(sparkle);
            }
        }

        // 添加3D悬停效果和触摸支持
        function add3DHoverEffect() {
            const cards = document.querySelectorAll('.weather-card');
            const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            
            cards.forEach(card => {
                if (!isTouchDevice) {
                    // 鼠标悬停效果 - 仅在非触摸设备上启用
                    card.addEventListener('mousemove', (e) => {
                        const rect = card.getBoundingClientRect();
                        const x = e.clientX - rect.left;
                        const y = e.clientY - rect.top;
                        
                        const centerX = rect.width / 2;
                        const centerY = rect.height / 2;
                        
                        const angleX = (y - centerY) / 20;
                        const angleY = (centerX - x) / 20;
                        
                        card.style.transform = `rotateX(${angleX}deg) rotateY(${angleY}deg) scale(1.05)`;
                    });
                    
                    card.addEventListener('mouseleave', () => {
                        card.style.transform = '';
                    });
                } else {
                    // 触摸设备上的效果
                    card.addEventListener('touchstart', () => {
                        // 添加按下效果
                        card.style.transform = 'scale(0.98)';
                        card.style.transition = 'transform 0.2s ease';
                    }, { passive: true });
                    
                    card.addEventListener('touchend', () => {
                        // 恢复正常大小
                        card.style.transform = '';
                        card.style.transition = 'transform 0.3s ease';
                    });
                }
            });
        }

        // 主题切换功能
        function initThemeToggle() {
            const themeToggle = document.getElementById('themeToggle');
            const rootElement = document.documentElement;
            
            // 检查本地存储中的主题设置
            const savedTheme = localStorage.getItem('weatherCardsTheme');
            if (savedTheme === 'light') {
                rootElement.classList.add('light-theme');
            }
            
            themeToggle.addEventListener('click', () => {
                rootElement.classList.toggle('light-theme');
                
                // 保存主题设置到本地存储
                const currentTheme = rootElement.classList.contains('light-theme') ? 'light' : 'dark';
                localStorage.setItem('weatherCardsTheme', currentTheme);
            });
        }

        // 页面加载完成后初始化动画和交互
        document.addEventListener('DOMContentLoaded', () => {
            createSunRays();
            createRaindrops();
            createWindElements();
            createSnowflakes();
            add3DHoverEffect();
            initThemeToggle();
        });
    </script>
</body>
</html>